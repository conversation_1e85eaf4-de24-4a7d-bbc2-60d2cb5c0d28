/* @jsxImportSource vue */
import { computed, defineComponent } from 'vue'
import type { PropType } from 'vue'
import { useQuestionsForm } from '@sa/hooks'
import styles from './index.module.less'

export default defineComponent({
  name: 'MultiChoice',
  props: {
    item: {
      type: Object as PropType<Question.ProcessedQuestionData>,
      required: true,
    },
    modelValue: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    type: {
      type: String as PropType<'edit' | 'answer' | 'preview'>,
      default: 'answer',
    },
  },
  emits: ['update:modelValue', 'update:item'],
  setup(props, { emit }) {
    const modelValue = computed({
      get() {
        return props.modelValue
      },
      set(value) {
        emit('update:modelValue', value)
      },
    })

    const { mergedDisabled } = useQuestionsForm({
      type: computed(() => props.type),
    })

    // 修改 item 属性的方法
    const updateItemProperty = (property: keyof Question.ProcessedQuestionData, value: any) => {
      const updatedItem = { ...props.item, [property]: value }
      emit('update:item', updatedItem)
    }

    const handleChange = (value: string) => {
      if (mergedDisabled.value)
        return
      const currentValues = [...modelValue.value]
      const selected = currentValues.includes(value)
      if (selected) {
        modelValue.value = currentValues.filter(item => item !== value)
      }
      else {
        modelValue.value = [...currentValues, value]
      }
    }

    return {
      // 暴露修改 item 属性的方法
      updateItemProperty,
      render() {
        return (
          <ul class={styles.root}>
            {props.item.options?.map(option => (
              <li>
                <label key={option.value} class={styles.choiceItem}>
                  <div
                    class={[
                      styles.choiceItemQn,
                      modelValue.value.includes(option.value) ? styles.choiceItemQnChecked : '',
                    ]}
                    onClick={() => {
                      if (mergedDisabled.value) {
                        return
                      }
                      handleChange(option.value)
                    }}
                  >
                    <span>{option.value}</span>
                  </div>
                  <span
                    class={[styles.choiceItemLabel, 'contents']}
                    v-html={option.label}
                    v-katex
                  />
                </label>
              </li>
            ))}
          </ul>
        )
      },
    }
  },
})
