/* @jsxImportSource vue */
import { defineComponent } from 'vue'
import { useQuestionsForm } from '@sa/hooks'
import styles from './index.module.less'

export default defineComponent({
  name: 'MultiChoice',
  props: {
    item: {
      type: Object as PropType<Question.ProcessedQuestionData>,
      required: true,
    },
    modelValue: {
      type: [String, Number],
      default: '',
    },
    type: {
      type: String as PropType<'edit' | 'answer' | 'preview'>,
      default: 'answer',
    },
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const modelValue = computed({
      get() {
        return props.modelValue
      },
      set(value) {
        emit('update:modelValue', value)
      },
    })

    const { mergedDisabled } = useQuestionsForm({
      type: computed(() => props.type),
    })

    const handleChange = (value: any) => {
      if (mergedDisabled.value)
        return
      const selected = modelValue.value.includes(value)
      if (selected) {
        modelValue.value = modelValue.value.filter(item => item !== value)
      }
      else {
        modelValue.value.push(value)
      }
    }

    return () => {
      return (
        <ul class={styles.root}>
          {props.options.map(option => (
            <li>
              <label key={option.value} class={styles.choiceItem}>
                <div
                  class={[
                    styles.choiceItemQn,
                    modelValue.value.includes(option.value) ? styles.choiceItemQnChecked : '',
                  ]}
                  onClick={() => {
                    if (mergedDisabled.value) {
                      return
                    }
                    handleChange(option.value)
                  }}
                >
                  <span>{option.value}</span>
                </div>
                <span
                  class={[styles.choiceItemLabel, 'contents']}
                  v-html={option.label}
                  v-katex
                />
              </label>
            </li>
          ))}
        </ul>
      )
    }
  },
})
