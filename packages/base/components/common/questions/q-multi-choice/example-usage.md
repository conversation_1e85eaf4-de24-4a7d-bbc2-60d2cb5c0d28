# 多选题组件中修改 item 属性的方法

## 方法一：通过组件实例调用 updateItemProperty 方法

```vue
<template>
  <div>
    <QMultiChoice 
      ref="multiChoiceRef"
      v-model:item="questionItem"
      v-model="selectedAnswers"
      :type="'edit'"
    />
    
    <!-- 示例按钮：修改题目标题 -->
    <button @click="updateTitle">修改题目标题</button>
    
    <!-- 示例按钮：添加新选项 -->
    <button @click="addOption">添加新选项</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import QMultiChoice from './index.tsx'

const multiChoiceRef = ref()
const questionItem = ref({
  id: '1',
  title: '原始题目标题',
  options: [
    { label: '选项A', value: 'A' },
    { label: '选项B', value: 'B' }
  ],
  // ... 其他属性
})
const selectedAnswers = ref([])

// 修改题目标题
function updateTitle() {
  multiChoiceRef.value?.updateItemProperty('title', '新的题目标题')
}

// 添加新选项
function addOption() {
  const newOptions = [
    ...questionItem.value.options,
    { label: '新选项C', value: 'C' }
  ]
  multiChoiceRef.value?.updateItemProperty('options', newOptions)
}
</script>
```

## 方法二：直接修改 v-model 绑定的数据

```vue
<template>
  <div>
    <QMultiChoice 
      v-model:item="questionItem"
      v-model="selectedAnswers"
      :type="'edit'"
    />
    
    <button @click="updateTitleDirectly">直接修改标题</button>
    <button @click="updateOptionsDirectly">直接修改选项</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const questionItem = ref({
  id: '1',
  title: '原始题目标题',
  options: [
    { label: '选项A', value: 'A' },
    { label: '选项B', value: 'B' }
  ],
  // ... 其他属性
})
const selectedAnswers = ref([])

// 直接修改标题
function updateTitleDirectly() {
  questionItem.value = {
    ...questionItem.value,
    title: '通过 v-model 修改的标题'
  }
}

// 直接修改选项
function updateOptionsDirectly() {
  questionItem.value = {
    ...questionItem.value,
    options: [
      ...questionItem.value.options,
      { label: '直接添加的选项', value: 'D' }
    ]
  }
}
</script>
```

## 可修改的 item 属性

根据 `Question.ProcessedQuestionData` 类型定义，可以修改以下属性：

- `id`: 题目唯一标识符
- `typeText`: 题型文本描述
- `typeId`: 题型ID
- `title`: 题目内容/题干
- `componentsName`: 对应前端组件名称
- `options`: 题目选项数组
- `correctAnswer`: 正确答案
- `analysis`: 题目解析对象
- `knowledgePoints`: 关联的知识点列表

## 注意事项

1. 使用方法一时，需要确保组件已经挂载完成
2. 使用方法二更直接，但需要注意保持数据的不可变性
3. 修改 `options` 时，确保每个选项都有 `label` 和 `value` 属性
4. 修改后的数据会自动同步到父组件（通过 v-model:item）
