# 题目组件 v-model 使用指南

## 问题说明

之前在 `QuestionItemContainer` 组件中同时使用了：
```vue
v-model="itemInfo"
v-model:item="itemInfo"
```

这会造成冲突，因为：
- `v-model="itemInfo"` → `:modelValue="itemInfo"` + `@update:modelValue`
- `v-model:item="itemInfo"` → `:item="itemInfo"` + `@update:item`

同一个数据被绑定到两个不同的 prop，会导致混乱。

## 正确的用法

### 在题目组件中的设计

每个题目组件（如 `QMultiChoice`、`QSingleChoice`）有两个主要的绑定：

1. **`v-model`（默认绑定到 `modelValue`）**：用于绑定**用户的答案**
   - 单选题：`string` 类型
   - 多选题：`string[]` 类型

2. **`v-model:item`**：用于绑定**题目数据本身**
   - 类型：`Question.ProcessedQuestionData`

### 在容器组件中的正确用法

```vue
<template>
  <component
    :is="activeModule.component"
    v-model="userAnswer"        <!-- 用户答案 -->
    v-model:item="itemInfo"     <!-- 题目数据 -->
    :type="props.type"
  />
</template>

<script setup>
// 题目数据
const itemInfo = defineModel<Question.ProcessedQuestionData>('itemInfo', {
  default: () => ({}),
})

// 用户答案
const userAnswer = defineModel<string | string[]>('userAnswer', {
  default: () => '',
})
</script>
```

### 在父组件中的使用

```vue
<template>
  <QuestionItemContainer
    v-model:item-info="questionData"    <!-- 题目数据 -->
    v-model:user-answer="userAnswer"    <!-- 用户答案 -->
    :type="'answer'"
  />
</template>

<script setup>
const questionData = ref({
  id: '1',
  title: '这是一道多选题',
  options: [
    { label: '选项A', value: 'A' },
    { label: '选项B', value: 'B' },
    { label: '选项C', value: 'C' }
  ],
  // ... 其他属性
})

const userAnswer = ref([]) // 多选题用数组，单选题用字符串
</script>
```

## 修改 item 属性的方法

### 方法一：通过 v-model:item 直接修改

```javascript
// 修改题目标题
questionData.value = {
  ...questionData.value,
  title: '新的题目标题'
}

// 添加新选项
questionData.value = {
  ...questionData.value,
  options: [
    ...questionData.value.options,
    { label: '新选项D', value: 'D' }
  ]
}
```

### 方法二：通过组件实例方法

```vue
<template>
  <QuestionItemContainer
    ref="questionRef"
    v-model:item-info="questionData"
    v-model:user-answer="userAnswer"
  />
</template>

<script setup>
const questionRef = ref()

function updateTitle() {
  // 如果子组件暴露了 updateItemProperty 方法
  questionRef.value?.updateItemProperty('title', '新标题')
}
</script>
```

## 总结

- **`v-model`**：绑定用户答案
- **`v-model:item`**：绑定题目数据
- 不要在同一个组件上重复绑定相同的数据
- 修改题目属性通过 `v-model:item` 绑定的数据进行
