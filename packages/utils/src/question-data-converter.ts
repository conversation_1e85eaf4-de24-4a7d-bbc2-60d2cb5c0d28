/**
 * 题型数据处理工具
 * 用于处理流式数据并转换为组件需要的格式
 */
import { nanoid } from '@sa/utils'

// // 定义流式数据的接口
// export interface StreamQuestionData {
//   Question: {
//     QuestionType: string
//     QuestionTypeId: string
//     Title: string
//     Options?: Array<{
//       Option: string
//       Content: string
//     }>
//     Answer: string
//     Analysis: string
//     KnowledgePoints: string[] | null
//   }
// }

// /**
//  * 处理后的题目数据结构
//  * 用于前端组件展示和交互的标准格式
//  */
// export interface ProcessedQuestionData {
//   /** 题目唯一标识符 */
//   id: string
//   /** 题型文本描述（如"单选题"、"多选题"等） */
//   typeText: string
//   /** 题型ID（对应后端定义的题型标识） */
//   typeId: string
//   /** 题目内容/题干 */
//   title: string
//   /** 对应前端组件名称（用于动态加载组件） */
//   componentsName: string
//   /**
//    * 题目选项（适用于选择题）
//    * @property label - 选项显示文本
//    * @property value - 选项实际值
//    */
//   options?: Array<{
//     label: string
//     value: string
//   }>
//   /** 正确答案（格式根据题型不同而变化） */
//   correctAnswer: string
//   /**
//    * 题目解析
//    * @property title - 解析标题
//    * @property content - 解析内容（数组形式支持多段落）
//    */
//   analysis: {
//     title: string
//     content: string[]
//   }
//   /** 关联的知识点列表 */
//   knowledgePoints: string[]
// }

/**
 * 处理单选题数据
 * @param data 原始流式数据，包含题目信息
 * @returns 处理后的单选题数据，符合ProcessedQuestionData接口格式
 */
function processSingleChoiceData(data: Question.QuestionData): Question.ProcessedQuestionData {
  // 解构原始数据并设置默认值
  const {
    QuestionType: typeText, // 题型文本描述
    QuestionTypeId: typeId, // 题型ID
    Title: title, // 题目内容
    Options = [], // 选项数组，默认为空数组
    Answer: correctAnswer, // 正确答案
    Analysis, // 答案解析
    KnowledgePoints = [], // 知识点数组，默认为空数组
  } = data.Question

  // 返回处理后的数据结构
  return {
    id: nanoid(), // 使用nanoid生成唯一ID
    componentsName: getQuestionComponentName(typeId), // 获取对应组件名称
    typeText, // 题型文本
    typeId, // 题型ID
    title, // 题目内容
    options: Options.map(option => ({ // 转换选项格式
      label: option.Content, // 选项显示文本
      value: option.Option, // 选项值
    })),
    correctAnswer, // 正确答案
    analysis: { // 解析内容
      title: '正确答案：', // 解析标题
      content: [Analysis], // 解析内容数组
    },
    knowledgePoints: KnowledgePoints || [], // 知识点数组
  }
}

/**
 * 处理多选题数据
 */
// function processMultipleChoiceData(data: StreamQuestionData): ProcessedQuestionData {
//   const options = data.Question.Options?.map(option => ({
//     label: option.Content,
//     value: option.Option,
//   })) || []

//   return {
//     id: `question_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
//     type: data.Question.QuestionType,
//     typeId: data.Question.QuestionTypeId,
//     title: data.Question.Title,
//     content: data.Question.Title,
//     options,
//     correctAnswer: data.Question.Answer,
//     analysis: {
//       title: '答案解析：',
//       content: [data.Question.Analysis],
//     },
//     knowledgePoints: data.Question.KnowledgePoints || [],
//     progress: data.Progress,
//   }
// }

/**
 * 处理判断题数据
 */
// function processTrueFalseData(data: StreamQuestionData): ProcessedQuestionData {
//   const options = [
//     { label: '正确', value: 'T' },
//     { label: '错误', value: 'F' },
//   ]

//   return {
//     id: `question_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
//     type: data.Question.QuestionType,
//     typeId: data.Question.QuestionTypeId,
//     title: data.Question.Title,
//     content: data.Question.Title,
//     options,
//     correctAnswer: data.Question.Answer,
//     analysis: {
//       title: '答案解析：',
//       content: [data.Question.Analysis],
//     },
//     knowledgePoints: data.Question.KnowledgePoints || [],
//     progress: data.Progress,
//   }
// }

/**
 * 处理填空题数据
 */
// function processFillBlankData(data: StreamQuestionData): ProcessedQuestionData {
//   return {
//     id: `question_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
//     type: data.Question.QuestionType,
//     typeId: data.Question.QuestionTypeId,
//     title: data.Question.Title,
//     content: data.Question.Title,
//     correctAnswer: data.Question.Answer,
//     analysis: {
//       title: '答案解析：',
//       content: [data.Question.Analysis],
//     },
//     knowledgePoints: data.Question.KnowledgePoints || [],
//     progress: data.Progress,
//   }
// }

/**
 * 处理简答题数据
 */
// function processShortAnswerData(data: StreamQuestionData): ProcessedQuestionData {
//   return {
//     id: `question_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
//     type: data.Question.QuestionType,
//     typeId: data.Question.QuestionTypeId,
//     title: data.Question.Title,
//     content: data.Question.Title,
//     correctAnswer: data.Question.Answer,
//     analysis: {
//       title: '答案解析：',
//       content: [data.Question.Analysis],
//     },
//     knowledgePoints: data.Question.KnowledgePoints || [],
//     progress: data.Progress,
//   }
// }

/**
 * 主要的数据处理函数
 * 根据题型ID处理不同类型的题目数据
 */
export function processQuestionData(data: Question.QuestionData): Question.ProcessedQuestionData | null {
  if (!data.Question) {
    return null
  }

  const questionTypeId = data.Question.QuestionTypeId
  switch (questionTypeId) {
    case '2': // 单选题
      return processSingleChoiceData(data)
    case '11': // 判断题
      return processSingleChoiceData(data)
    default:
      return processSingleChoiceData(data)
  }
  // switch (questionTypeId) {
  //   case '1': // 多选题
  //     // return processMultipleChoiceData(data)
  //   case '2': // 单选题
  //     return processSingleChoiceData(data)
  //   case '3': // 判断题
  //     // return processTrueFalseData(data)
  //   case '4': // 填空题
  //     // return processFillBlankData(data)
  //   case '5': // 简答题
  //     // return processShortAnswerData(data)
  //   case '6': // 论述题
  //     // return processShortAnswerData(data) // 论述题和简答题处理方式相同
  //   default:
  //     console.warn(`未知的题型ID: ${questionTypeId}`)
  //     return processSingleChoiceData(data) // 默认按单选题处理
  // }
}

/**
 * 获取题型对应的组件名称
 */
function getQuestionComponentName(typeId: string): string {
  const componentMap: Record<string, string> = {
    1: 'MultipleChoice',
    2: 'single-choice',
    11: 'single-choice',
    3: 'TrueFalse',
    4: 'FillBlank',
    5: 'ShortAnswer',
    6: 'Essay',
  }

  return componentMap[typeId] || 'SingleChoice'
}
